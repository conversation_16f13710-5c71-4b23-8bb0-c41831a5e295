# 编译器
ARM_COMPILER := arm-hisiv100nptl-linux-
CC           := ${ARM_COMPILER}gcc
CPP          := ${ARM_COMPILER}g++
LD           := ${ARM_COMPILER}ld
AR           := ${ARM_COMPILER}ar
STRIP        := ${ARM_COMPILER}strip

CFLAGS += -c -g -Wall
CFLAGS += $(INCLUDE)

# 头文件路径
INCLUDE += 

# gsoap库目录名
LIBGSOAP_DIR =.

# gsoap库文件名
LIBNAME=gsoap_onvif

LIBPREF=lib
LIBSUF=.a
LIBGSOAP=$(LIBPREF)$(LIBNAME)$(LIBSUF)

SLIBPREF=lib
SLIBSUF=.so
SLIBGSOAP=$(SLIBPREF)$(LIBNAME)$(SLIBSUF)

# 源文件
SOURCES += empty_so_gsoap.c

# 目标文件
OBJECTS := $(patsubst %.c,$(TEMPDIR)%.o,$(filter %.c, $(SOURCES)))

all: $(SLIBGSOAP) 

clean:
	-rm -f $(OBJECTS)
	-rm -f $(LIBGSOAP_DIR)/$(LIBGSOAP)
	-rm -f $(LIBGSOAP_DIR)/$(SLIBGSOAP)

$(SLIBGSOAP): $(OBJECTS)
	@echo "  LD      " $@;
	@mkdir -p $(LIBGSOAP_DIR)
	@$(CC) -shared -o $(LIBGSOAP_DIR)/$(SLIBGSOAP) $(OBJECTS)

%.o: %.c
	@echo "  CC      " $@;
	@$(CC) $(CFLAGS) -c -o $@ $<

.PHONY: all clean
