
# Use this file as the following sample
# ifeq ($(APP_PARAM_FILE), )
#     APP_PARAM_FILE:=../Makefile.base
#     include $(APP_PARAM_FILE)
# endif

export APP_PARAM_FILE
export APP_PATH?=$(shell cd $(PWD)/`dirname $(APP_PARAM_FILE)`/..; pwd)
export APP_PATH_ENTRY?=$(shell cd $(PWD)/`dirname $(APP_PARAM_FILE)`/; pwd)

INCLUDES :=
INCLUDES += -I$(APP_PATH_ENTRY)/comuserv/h
CFLAGS   :=
CFLAGS   += -DPLT_EC20

LDFLAGS  :=
LDFLAGS  += -L$(APP_PATH_ENTRY)/comuserv/_build
LDFLAGS  += -L$(SDKTARGETSYSROOT)/usr/lib
LDFLAGS  += -L$(APP_PATH_ENTRY)/maint_if/_build

LIBS     :=
LIBS     += -ldl
LIBS     += $(SDKTARGETSYSROOT)/usr/lib/liblog.so
LIBS     += -lcurl
