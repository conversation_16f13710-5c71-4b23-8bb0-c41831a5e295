/*************************************************************
*
*   文件名:    yx_plt_gpiointerface.c
*   创建者:    RF@YAXON
*
*　 版本号:    1.0
*   文件描述:  通用输入输出接口封装模块
****************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <sys/select.h>
#include <sys/time.h>
#include <errno.h>
#include <string.h> 
#include <sys/sem.h>

#include "yx_plt_system.h"
#include "yx_plt_gpioctl.h"
#include "yx_plt_gpiointerface.h"

/************************************************************************************************************************
程序调试函数及变量  ----start
************************************************************************************************************************/
#define MSG_ERR 	1
#define MSG_WARN 	2
#define MSG_INFO 	3
#define MSG_DBG 	4
#define MSG_NOR 	4

#define MSG_HEAD 	("yx_plt_gpiointerface")

#define PRTMSG(level, fmt, args...)\
do {\
	if (level <= MSG_NOR) {\
	    if (level <= MSG_ERR) {\
		    printf("%s, %s, line %d: " fmt,__FILE__,__FUNCTION__,__LINE__, ##args);\
	    } else {\
		    printf("%s:" fmt, MSG_HEAD, ##args);\
		}\
	}\
} while(0)

/************************************************************************************************************************
程序调试函数及变量  ----end
************************************************************************************************************************/
void YX_PLT_GPSPWRON(void)
{
    #ifdef PLT_EC20
    YX_PLT_ReleaseOutputPin(TYPE_GPSPOW_);
    #endif
}

void YX_PLT_GPSPWROFF(void)
{
    #ifdef PLT_EC20
    YX_PLT_ControlOutputPin(TYPE_GPSPOW_);
    #endif
}

void YX_PLT_TFPWRON(void)
{
    #ifdef PLT_EC20
    YX_PLT_ReleaseOutputPin(TYPE_TFPWR_);
    #endif
}

void YX_PLT_TFPWROFF(void)
{
    #ifdef PLT_EC20
    YX_PLT_ControlOutputPin(TYPE_TFPWR_);
    #endif
}

void YX_PLT_232PWRON(void)
{
    #ifdef PLT_EC20
    YX_PLT_ReleaseOutputPin(TYPE_232POW_);
    #endif
}

void YX_PLT_232PWROFF(void)
{
    #ifdef PLT_EC20
    YX_PLT_ControlOutputPin(TYPE_232POW_);
    #endif
}

void YX_PLT_GPSSTATEON(void)
{
    #ifdef PLT_EC20
    YX_PLT_ControlOutputPin(TYPE_GPSSTATE_);
    #endif
}

void YX_PLT_GPSSTATEOFF(void)
{
    #ifdef PLT_EC20
    YX_PLT_ReleaseOutputPin(TYPE_GPSSTATE_);
    #endif
}

void YX_PLT_GPSWARMON(void)
{
    #ifdef PLT_EC20
    YX_PLT_ControlOutputPin(TYPE_GPSWARM_);
    #endif
}

void YX_PLT_GPSWARMOFF(void)
{
    #ifdef PLT_EC20
    YX_PLT_ReleaseOutputPin(TYPE_GPSWARM_);
    #endif
}

void YX_PLT_GAMEPADON(void)
{
    #ifdef PLT_EC20
    YX_PLT_ReleaseOutputPin(TYPE_GAMEPAD_);
    #endif
}

void YX_PLT_GAMEPADOFF(void)
{
    #ifdef PLT_EC20
    YX_PLT_ControlOutputPin(TYPE_GAMEPAD_);
    #endif
}

void YX_PLT_UARTPWRON(void)
{
    #ifdef PLT_EC20
    YX_PLT_ReleaseOutputPin(TYPE_UARTPOW_);
    #endif
}

void YX_PLT_UARTPWROFF(void)
{
    #ifdef PLT_EC20
    YX_PLT_ControlOutputPin(TYPE_UARTPOW_);
    #endif
}

// DINGDENG
void YX_PLT_DingDengLighten(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ControlOutputPin(TYPE_DINGDENG_);
    #endif
}

void YX_PLT_DingDengExtinguish(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ReleaseOutputPin(TYPE_DINGDENG_);
    #endif
}

// WIFI或者镜像存储的电源控制
void YX_PLT_JXPWRON(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ControlOutputPin(TYPE_JXPWR_);
    #endif
}

void YX_PLT_JXPWROFF(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ReleaseOutputPin(TYPE_JXPWR_);
    #endif
}

// sx_power, 摄像头电源控制
void YX_PLT_SXPowerOn(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ControlOutputPin(TYPE_SXPWR_);
    #endif
}

void YX_PLT_SXPowerOff(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ReleaseOutputPin(TYPE_SXPWR_);
    #endif
}

// PHONE_TTS
void YX_PLT_SelectTTSSpeaker(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ControlOutputPin(TYPE_PHONETTS_);
    #endif
}

void YX_PLT_SelectPhoneSpeaker(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ReleaseOutputPin(TYPE_PHONETTS_);
    #endif
}

// RFID
void YX_PLT_RFIDPowerOn(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ControlOutputPin(TYPE_RFIDPWR_);
    #endif
}

void YX_PLT_RFIDPowerOff(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ReleaseOutputPin(TYPE_RFIDPWR_);
    #endif
}

//pa_pd 2W功放电源控制
void YX_PLT_OpenAmplifier(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ControlOutputPin(TYPE_PAPD_);
    #endif

    #ifdef PLT_EC20
    YX_PLT_ReleaseOutputPin(TYPE_PAPD_);
    #endif
}

void YX_PLT_CloseAmplifier(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ReleaseOutputPin(TYPE_PAPD_);
    #endif

    #ifdef PLT_EC20
    YX_PLT_ControlOutputPin(TYPE_PAPD_);
    #endif
}

//LCD_POWER LCD电源控制
void YX_PLT_LCDPowerOn(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ControlOutputPin(TYPE_LCDPWR_);
    #endif
}

void YX_PLT_LCDPowerOff(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ReleaseOutputPin(TYPE_LCDPWR_);
    #endif
}

// SD1_RESET
void YX_PLT_SDReset_Reset(void)
{
    //YX_PLT_ControlOutputPin(TYPE_SD1RST_);
}

void YX_PLT_SDReset_Release(void)
{
    //YX_PLT_ReleaseOutputPin(TYPE_SD1RST_);
}

///diaodu power
void YX_PLT_DiaoDuPowerOn(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ControlOutputPin(TYPE_DIAODUPWR_);
    #endif
}

void YX_PLT_DiaoDuPowerOff(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ReleaseOutputPin(TYPE_DIAODUPWR_);
    #endif
}

//yinxiang
void YX_PLT_YinXiangOpen(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ControlOutputPin(TYPE_YINXIANG_);
    #endif
}

void YX_PLT_YinXiangClose(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ReleaseOutputPin(TYPE_YINXIANG_);
    #endif
}

//ADC reset
void YX_PLT_ADCReset_Reset(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ControlOutputPin(TYPE_ADCRST_);
    #endif
}

void YX_PLT_ADCReset_Release(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ReleaseOutputPin(TYPE_ADCRST_);
    #endif
}

//ADC POWER AD芯片电源控制
void YX_PLT_ADCPowerOn(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ControlOutputPin(TYPE_ADPOW_);
    #endif
}

void YX_PLT_ADCPowerOff(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ReleaseOutputPin(TYPE_ADPOW_);
    #endif
}

//该引脚用作为2G/3G模块检测
//PATH_COL 录音和MIC通话选择
void YX_PLT_SelectMIC(void)
{
//    YX_PLT_ControlOutputPin(TYPE_PATHCOL_);
}

void YX_PLT_SelectRecord(void)
{
//    YX_PLT_ReleaseOutputPin(TYPE_PATHCOL_);
}


//HUB RST HUB复位控制
void YX_PLT_HUBReset_Reset(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ControlOutputPin(TYPE_HUBRST_);
    #endif
}

void YX_PLT_HUBReset_Release(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ReleaseOutputPin(TYPE_HUBRST_);
    #endif
}

//HD_SAVE 硬盘供电控制：
void YX_PLT_HDPowerOn(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ControlOutputPin(TYPE_HDSAVE_);
    #endif
}

void YX_PLT_HDPowerOff(void)
{
    #ifdef PLT_HI3520D
    YX_PLT_ReleaseOutputPin(TYPE_HDSAVE_);
    #endif
}

// 流程控制类接口
void YX_PLT_ADCResetControl(void)
{
    GpioSem_P();
    YX_PLT_ADCReset_Reset();
    usleep(500*1000);
    YX_PLT_ADCReset_Release();
    GpioSem_V();  
}

void YX_PLT_SDResetControl(void)
{
    GpioSem_P();
    YX_PLT_SDReset_Reset();
    usleep(500*1000);
    YX_PLT_SDReset_Release();
    GpioSem_V();  
}

void YX_PLT_HUBResetControl(void)
{
    GpioSem_P();
    YX_PLT_HUBReset_Reset();
    usleep(500*1000);
    YX_PLT_HUBReset_Release();
    GpioSem_V(); 
}

BOOLEAN YX_GPT_RegisterSensorTriggerInform(INT32U type, INT32U trigmode, void (*informer)(INT32U, INT32U))
{ 
    return YX_PLT_SensorChangeCallback(type, trigmode, informer);
}

INT32U YX_GPT_SensorValid_FILTER_Interface(INT32U type)
{
    return YX_PLT_SensorValid_FILTER(type); 
}

BOOLEAN YX_GPT_IsSensorTrigger_Interface(INT32U type)
{ 
    return YX_PLT_IsSensorTrigger(type);
}

void YX_GPT_StartFlashPort(INT32U type, INT32U mode, INT32U cycle, INT32U time_high, INT32U time_low)
{
    YX_PLT_StartFlashPort(type, mode, cycle, time_high, time_low);
}

void YX_GPT_StopFlashPort(INT32U type, INT32U mode)
{
    YX_PLT_StopFlashPort(type, mode);
}

void YX_GPT_InstallPermnentPort(INT32U type, INT32U time_high, INT32U time_low)
{
    YX_PLT_InstallPermnentPort(type, time_high, time_low);
}

void YX_GPT_RemovePermnentPort(INT32U type)
{
    YX_PLT_RemovePermnentPort(type);
}
void YX_GPT_InformOilCutOFF(void)
{
    YX_PLT_InformOilCutOFF();
}

void YX_GPT_InformOilSwitchOn(void)
{
    YX_PLT_InformOilSwitchOn();
}

void YX_GPT_SetOilTriggerMode(BOOLEAN ismonostable)
{
    YX_PLT_SetOilTriggerMode(ismonostable);
}
