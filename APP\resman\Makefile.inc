
#include path
INCLUDES    += -I./h
INCLUDES    += -I../fk/h
INCLUDES    += -I../resman_if/h
INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/qmi-framework
INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/data
INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/qmi
INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/quectel-openlinux-sdk
INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/dsutils
INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/

# source files, sub directory will then add to this, all source files must use ".c" as the extension name        
SOURCES += c/fk_application.c \
        c/rm_version.c \
		c/rm_pmsg_dispatcher.c \
		c/rm_utils.c \
		c/rm_protocol.c \
		c/rm_comm.c \
		c/rm_comm_dispatcher.c \
		c/rm_comm_sndbuf.c \
		c/rm_ad.c \
        c/rm_ctl.c \
		c/rm_device.c \
		c/rm_firmware.c \
		c/rm_iccard.c \
		c/rm_keyboard.c \
		c/rm_nmea0183.c \
        c/rm_location_port.c \
        c/rm_location_port_ec20.c \
		c/rm_location.c \
		c/rm_location_module_ublox.c\
		c/rm_location_module_zkw.c\
		c/rm_location_module_ga302.c\
		c/rm_milpulse.c \
		c/rm_sensor.c \
		c/rm_can.c \
		c/rm_gsensor.c \
		c/rm_realclock.c \
		c/rm_uartext.c \
		c/rm_server_ad.c \
		c/rm_server_ctl.c \
		c/rm_server_device.c \
		c/rm_server_firmware.c \
		c/rm_server_iccard.c \
		c/rm_server_keyboard.c \
		c/rm_server_location.c \
		c/rm_server_milpulse.c \
		c/rm_server_sensor.c \
		c/rm_server_can.c \
		c/rm_server_gsensor.c \
		c/rm_server_realclock.c \
		c/rm_server_uartext.c \
		c/rm_server_extstrg.c

# sub directories, sub directory will then add to this
SUBDIRS		+= ./c

