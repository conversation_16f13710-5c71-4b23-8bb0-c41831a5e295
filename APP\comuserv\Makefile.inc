
#include path
INCLUDES    += -I./h

INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/data
INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/qmi
INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/quectel-openlinux-sdk
INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/dsutils
INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/qmi-framework
INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/

# source files, sub directory will then add to this, all source files must use ".c" as the extension name        
SOURCES += c/yx_plt_gpioctl.c \
		c/yx_plt_gpiointerface.c \
		c/yx_plt_amplifier.c \
		c/yx_plt_uartdrv.c \
		c/yx_plt_beep.c \
		c/yx_plt_hdisk_powerctl.c \
		c/yx_plt_ipc_ctl.c \

# sub directories, sub directory will then add to this
SUBDIRS		+= ./c

