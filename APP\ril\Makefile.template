
###############################################################################
# I hope that you don't need to modify below at all !
###############################################################################

# define compiler tools
CC		:= ${ARM_COMPILER}gcc
LD		:= ${ARM_COMPILER}ld
AR		:= ${ARM_COMPILER}ar
STRIP	:= ${ARM_COMPILER}strip

# define build dir for target, and temp dir for objects
BUILDDIR	?= _build
TEMPDIR		?= _temp

# apply above configure
CFLAGS		+= $(INCLUDES) $(DEFINES)
LDFLAGS		+= $(LIBS)

ifeq ($(TEMPLATE),lib)
ifeq ($(findstring shared,$(CONFIG)),shared)
TARGET		:= lib$(TARGET).so
CFLAGS		+= -fpic
LDFLAGS		+= -shared
endif
ifeq ($(findstring static,$(CONFIG)),static)
TARGET		:= lib$(TARGET).a
endif
endif

# determine the object files
OBJECTS		:= $(patsubst %.c,$(TEMPDIR)/%.o,$(filter %.c, $(SOURCES)))
DEPENDS		:= $(patsubst %.c,$(TEMPDIR)/%.d,$(filter %.c, $(SOURCES)))

.PHONY: usage
usage:
	@echo "Build Help for $(TARGET)"
	@echo "    make all        -- build the entire project "
	@echo "    make install    -- install to target "
	@echo "    make clean      -- clean project "
	@echo "    make distclean  -- deeply clean project "
	@echo "    make rebuild    -- build the project in the right order "
	echo 
	
# build all
.PHONY: all
all: mngdir mksubpaths $(TARGET)

mngdir:
	@echo "Building for $(TEMPDIR) ..."
	@mkdir -p $(TEMPDIR)
	@mkdir -p $(BUILDDIR)

mksubpaths:
	@echo "subdir... $(SUBDIRS)"
	@for subdir in $(SUBDIRS); do \
		echo "mkdir -p $(TEMPDIR)/$$subdir";\
		mkdir -p $(TEMPDIR)/$$subdir; \
	done

$(TARGET): $(OBJECTS) $(DEPENDS)
	@echo "Building for $(TARGET) ..."
	@mkdir -p $(BUILDDIR)
	
ifeq ($(TEMPLATE),app)
	$(CC) $(CFLAGS) -o $(BUILDDIR)/$@ $(OBJECTS) $(LDFLAGS)
	@cp $(BUILDDIR)/$(TARGET) $(BUILDDIR)/$(TARGET).debug; 
	@$(STRIP) -g $(BUILDDIR)/$(TARGET);
endif
ifeq ($(TEMPLATE),lib)
ifeq ($(findstring shared,$(CONFIG)),shared)
	$(CC) $(CFLAGS) -o $(BUILDDIR)/$@ $(OBJECTS) $(LDFLAGS)
endif
ifeq ($(findstring static,$(CONFIG)),static)
	$(AR) -r $(BUILDDIR)/$@ $(OBJECTS)
endif
endif

# include the C include dependencies
ifeq ($(findstring all,$(MAKECMDGOALS)),all)
-include $(OBJECTS:.o=.d)
else
ifeq ($(findstring rebuild,$(MAKECMDGOALS)),rebuild)
-include $(OBJECTS:.o=.d)
endif
endif

# calculate C inlcude dependencies
$(TEMPDIR)/%.d: %.c
#	@echo "$@: $<:$(notdir $*)"
	@echo "  DEP      " $@;
	@mkdir -p $(@D)
	@set -e; rm -f $@; \
	$(CC) -MM $(CFLAGS) $< > $@.$$$$; \
	sed 's,$(notdir $*)\.o[ :]*,$(TEMPDIR)/$*\.o $@ : ,g' < $@.$$$$ > $@; \
	rm -f $@.$$$$

$(TEMPDIR)/%.o: %.c
	@mkdir -p $(@D)
	@echo "  CC      " $@;
	@$(CC) $(CFLAGS) -c -o $@ $<

# install
install:
	@echo "installing $(TARGET) ..."
	@cd $(BUILDDIR) && \
	cp -fv $(TARGET)* $(BINDIR)

# clean up project
.PHONY: clean distclean
clean:
	@echo "Cleaning $(TARGET) ..."
	-@rm -fv $(OBJECTS)
	-@rm -fv $(DEPENDS)

distclean: clean
	@echo "Deeply cleaning $(TARGET) ..."
	-@rm -fv $(BUILDDIR)/*

# rebuild
.PHONY: rebuild
rebuild: distclean all install
