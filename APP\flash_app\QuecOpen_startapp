#!/bin/sh
#
#  Copyright (C) 2016-2020 Quectel Wireless Co.,Ltd. 
#  Quectel Wireless Proprietary and Confidential.
# -----------------------------------------------
#
# Description: 
#   init.d script for Quectel QuecOpen application.
#
# Created by <PERSON><PERSON>.YONG
#
#############################################################
#

#set -e

LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/mnt/flash/app:/usrdata/yaxon
export LD_LIBRARY_PATH

# here user can indicate the user app path
AppProgram=/mnt/flash/app/monitor.exe
oemPreStart=/mnt/flash/app/oemPreStart.sh
oemPostStart=/mnt/flash/app/oemPostStart.sh

case "$1" in
       start)
                if [ ! -f "/mnt/sdisk/flashself_98765" ]; then
                    if [ -f "/usrdata/factory" ]; then
                        rm /mnt/sdisk/* -rf
                        rm /usrdata/factory
                    fi
                fi

                chmod 777 /sys/module/xt_recent/parameters/ip_pkt_list_tot
                echo "255" >  /sys/module/xt_recent/parameters/ip_pkt_list_tot
                telnetd -p 8020 &
                tcpsvd ************* 8021 ftpd -w / &

                if [ -f $oemPreStart ]
                then
					$oemPreStart
                fi

                if ! [ -f $AppProgram ]
                then
                    echo "### QuecOpen Application doesn't exist ###"
                    exit 3
                fi
                echo -n ">>> Starting QuecOpen application: "
                ln -s /dev/ttyHS1 /dev/ttyHSL2
		#start-stop-daemon -S -b -a $AppProgram
                $AppProgram &

                if [ -f $oemPostStart ]
                then
					$oemPostStart &
                fi

                echo "done"
                ;;
       stop)
                echo -n "Stopping quectel daemon: "
                killall tcpsvd
                start-stop-daemon -K -n AppImageBinV01
                echo "done"
                ;;
       restart)
                $0 stop
                $0 start
                ;;
       *)
                echo "Usage: QuecOpen_startapp { start | stop | restart }" >&2
                exit 1
                ;;
esac

need_update=1

exit 0
