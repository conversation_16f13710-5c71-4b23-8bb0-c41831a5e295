ifeq ($(PARAM_FILE), )
     PARAM_FILE:=../Makefile.param
     include $(PARAM_FILE)
endif

ifeq ($(APP_PARAM_FILE), )
     APP_PARAM_FILE:=../Makefile.base
     include $(APP_PARAM_FILE)
endif

#define complier
ARM_COMPILER = arm-oe-linux-gnueabi-

# config version
PLATFORM=
APPTYPE=resman_if
VERSION=

# target
TARGET		:= $(strip $(PLATFORM))$(strip $(APPTYPE))$(strip $(VERSION))

# define build template, app / lib
TEMPLATE	:= lib

# define configure, static / shared if lib
CONFIG		+= static

# default install dir
BINDIR		?= $(APP_PATH)/APP/build/
	
# external libraries
LIBS		+= -lpthread ../fk/_build/libframework.a

# defines
DEFINES		+= 

# compile flags
#CFLAGS		+= -Wall -O2 -Wno-uninitialized -fno-strict-aliasing
CFLAGS		+= -mapcs -rdynamic -funwind-tables -g -Wall -O2 -Wno-uninitialized -fno-strict-aliasing

#LDFLAGS    += -L../comuserv

include ./Makefile.inc
#include $(APP_PATH)/gsmat/ext_make/Makefile.pathgsm
include ./Makefile.template
