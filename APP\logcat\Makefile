ifeq ($(PARAM_FILE), )
     PARAM_FILE:=../Makefile.param
     include $(PARAM_FILE)
endif

ifeq ($(APP_PARAM_FILE), )
     APP_PARAM_FILE:=../Makefile.base
     include $(APP_PARAM_FILE)
endif

#define complier
ARM_COMPILER = arm-oe-linux-gnueabi-

# config version
PLATFORM=
APPTYPE=logcat
VERSION=

# target
TARGET		:= $(strip $(PLATFORM))$(strip $(APPTYPE))$(strip $(VERSION))

# define build template, app / lib
TEMPLATE	:= app

# define configure, static / shared if lib
CONFIG		+= static

# default install dir
BINDIR		?= $(APP_PATH)/APP/build/
	
# external libraries
LIBS		+= -lpthread
LIBS            += -L../maint_if/_build/ -lmaint_if

# defines
DEFINES		+= 

# compile flags
#CFLAGS		+= -Wall -O2 -Wno-uninitialized -fno-strict-aliasing
CFLAGS		= -Wall -O2

#LDFLAGS    += -L../fk

include ./Makefile.inc
#include $(APP_PATH)/gsmat/ext_make/Makefile.pathgsm
include ./Makefile.template
