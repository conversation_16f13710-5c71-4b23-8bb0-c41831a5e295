
#include path
INCLUDES	+= -I.
INCLUDES    += -I../fk/h
INCLUDES    += -I./h
INCLUDES    += -I../ril_if/h
INCLUDES    += -I../resman_if/h
INCLUDES    += -I../monitor/h
INCLUDES    += -I../../mpp/include/extdrv

INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/qmi-framework
INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/data
INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/qmi
INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/quectel-openlinux-sdk
INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/dsutils
INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/
INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/lib

# source files, sub directory will then add to this, all source files must use ".c" as the extension name

SOURCES += c/fk_application.c
SOURCES += c/ril_version.c
SOURCES += c/ril_utils.c
SOURCES += c/ril_utils_fuser.c
SOURCES += c/ril_timer.c
SOURCES += c/ril_port_hardware.c
SOURCES += c/ril_port_net.c
SOURCES += c/ril_port_driver.c
SOURCES += c/ril_serial_port.c
SOURCES += c/ril_comm_adapter_serial_port.c
SOURCES += c/ril_ucs2_to_gb.c
SOURCES += c/ril_gb_to_ucs2.c
SOURCES += c/ril_pdu.c
SOURCES += c/ril_pdu_cdma_sim6320.c
SOURCES += c/ril_at_tok.c
SOURCES += c/ril_at_channel.c
SOURCES += c/ril_at_solcmd_que.c
SOURCES += c/ril_at_solcmd_analyzer.c
SOURCES += c/ril_at_solcmd_analyzer_str.c
SOURCES += c/ril_at_solcmd_analyzer_ati.c
SOURCES += c/ril_at_solcmd_analyzer_csq.c
SOURCES += c/ril_at_solcmd_analyzer_cfun.c
SOURCES += c/ril_at_solcmd_analyzer_cregq.c
SOURCES += c/ril_at_solcmd_analyzer_cnmiq.c
SOURCES += c/ril_at_solcmd_analyzer_cscaq.c
SOURCES += c/ril_at_solcmd_analyzer_clcc.c
SOURCES += c/ril_at_solcmd_analyzer_cimi.c
SOURCES += c/ril_at_solcmd_analyzer_cgsn.c
SOURCES += c/ril_at_solcmd_analyzer_ciccid.c
SOURCES += c/ril_at_solcmd_analyzer_qccid_m12.c
SOURCES += c/ril_at_solcmd_analyzer_cnum.c
SOURCES += c/ril_at_solcmd_analyzer_cpinq.c
SOURCES += c/ril_at_solcmd_analyzer_copsq.c
SOURCES += c/ril_at_solcmd_analyzer_qsimdet.c
SOURCES += c/ril_at_solcmd_analyzer_cnsmodq_sim5360.c
SOURCES += c/ril_at_solcmd_analyzer_cnsmodq_sim6320.c
SOURCES += c/ril_at_solcmd_analyzer_cnsmodq_sim7100.c
SOURCES += c/ril_at_solcmd_analyzer_cnsmodq_sim7600.c
SOURCES += c/ril_at_solcmd_analyzer_ceregq_sim7100.c
SOURCES += c/ril_at_solcmd_analyzer_ceregq_sim7600.c
SOURCES += c/ril_at_solcmd_analyzer_ceregq_ec20.c
SOURCES += c/ril_at_solcmd_analyzer_cgregq_sim7600.c
SOURCES += c/ril_at_solcmd_analyzer_cpsiq_sim5360.c
SOURCES += c/ril_at_solcmd_analyzer_cpsiq_sim6320.c
SOURCES += c/ril_at_solcmd_analyzer_cpsiq_sim7100.c
SOURCES += c/ril_at_solcmd_analyzer_cpsiq_sim7600.c
SOURCES += c/ril_at_solcmd_analyzer_qnwinfo_ec20.c
SOURCES += c/ril_at_unsolcmd_dispatcher.c
SOURCES += c/ril_at_unsolcmd_analyzer.c
SOURCES += c/ril_at_unsolcmd_analyzer_cmt.c
SOURCES += c/ril_at_unsolcmd_analyzer_cmgl.c
SOURCES += c/ril_at_unsolcmd_analyzer_cmt_sim6320.c
SOURCES += c/ril_at_unsolcmd_analyzer_cmgl_sim6320.c
SOURCES += c/ril_at_unsolcmd_analyzer_clip.c
SOURCES += c/ril_multiplexer.c
SOURCES += c/ril_power.c
SOURCES += c/ril_ending_adapter.c
SOURCES += c/ril_ending_adapter_general.c
SOURCES += c/ril_telephony.c
SOURCES += c/ril_telephony_adapter.c
SOURCES += c/ril_telephony_adapter_gsm.c
SOURCES += c/ril_telephony_adapter_sim5360.c
SOURCES += c/ril_telephony_adapter_sim6320.c
SOURCES += c/ril_telephony_adapter_sim7100.c
SOURCES += c/ril_telephony_adapter_sim7600.c
SOURCES += c/ril_telephony_adapter_m12.c
SOURCES += c/ril_telephony_adapter_ec20.c
SOURCES += c/ril_call.c
SOURCES += c/ril_call_adapter.c
SOURCES += c/ril_call_adapter_sim5360.c
SOURCES += c/ril_call_adapter_sim6320.c
SOURCES += c/ril_call_adapter_sim7100.c
SOURCES += c/ril_call_adapter_sim7600.c
SOURCES += c/ril_call_adapter_m12.c
SOURCES += c/ril_call_adapter_ec20.c
SOURCES += c/ril_sms.c
SOURCES += c/ril_sms_adapter.c
SOURCES += c/ril_sms_adapter_gsm.c
SOURCES += c/ril_sms_adapter_cdma.c
SOURCES += c/ril_sms_adapter_sim6320.c
SOURCES += c/ril_dialer.c
SOURCES += c/ril_dialer_adapter.c
SOURCES += c/ril_dialer_adapter_sim5360.c
SOURCES += c/ril_dialer_adapter_sim6320.c
SOURCES += c/ril_dialer_adapter_sim7100.c
SOURCES += c/ril_dialer_adapter_sim7600.c
SOURCES += c/ril_dialer_adapter_m12.c
SOURCES += c/ril_dialer_adapter_ec20.c
SOURCES += c/ril_update.c
SOURCES += c/ril_update_adapter.c
SOURCES += c/ril_update_adapter_sim7600.c
SOURCES += c/ril_update_adapter_sim7100.c
SOURCES += c/ril_adapter_factory.c
SOURCES += c/ril_module_detector_usb.c
SOURCES += c/ril_module_detector_uart.c
SOURCES += c/ril_module_detector.c
SOURCES += c/ril_manager.c
SOURCES += c/ril_pmsg_dispatcher.c
SOURCES += c/ril_server_telephony.c
SOURCES += c/ril_server_call.c
SOURCES += c/ril_server_sms.c
SOURCES += c/ril_server_dialer.c
SOURCES += c/ril_server_update.c
        		
# sub directories, sub directory will then add to this
SUBDIRS		+= ./c

