#!/bin/sh
#
#被monitor.exe调用,运行顺序先于业务程序
#通过本脚本可以执行一些应用程序运行前的控制
#可以通过应用程序直接升级本脚本
#
#############################################################
#

LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/mnt/flash/app
export LD_LIBRARY_PATH

FIND_PARTITION_FILE="/mnt/flash/app/find_partitions.sh"
ETC_FIND_PARTITION_FILE="/etc/init.d/find_partitions.sh"

STARTAPP_FILE="/mnt/flash/app/QuecOpen_startapp"
ETC_STARTAPP_FILE="/etc/init.d/QuecOpen_startapp"

partitions_check_func () {
	if [ -f "$FIND_PARTITION_FILE" ];then
		diff $FIND_PARTITION_FILE $ETC_FIND_PARTITION_FILE >/dev/null
		if [ $? == 0 ];	then
			echo "Both file same"
			return 0;
		fi

		flag=`cat $FIND_PARTITION_FILE  |grep need_update`
		if [ -n "$flag" ]; then
			cp "$FIND_PARTITION_FILE" "$ETC_FIND_PARTITION_FILE"
			chmod + "$ETC_FIND_PARTITION_FILE"
			#rm "$FIND_PARTITION_FILE"
			rm /data/*_format_info.txt
			rm /data/*_reboot_info.txt 
			sync
		fi
	fi
	return 0
}

startapp_check_func () {
	if [ -f "$STARTAPP_FILE" ];then
		diff $STARTAPP_FILE $ETC_STARTAPP_FILE >/dev/null
		if [ $? == 0 ];	then
			echo "Both file same"
			return 0;
		fi

		flag=`cat $STARTAPP_FILE  |grep need_update`
		if [ -n "$flag" ]; then
			cp "$STARTAPP_FILE" "$ETC_STARTAPP_FILE"
			chmod +x "$ETC_STARTAPP_FILE"
			#rm "$STARTAPP_FILE" 
			sync
		fi
	fi
	return 0
}

usrdata_check_func () {
	if grep -qs '/usrdata' /proc/mounts;then
		echo "It's mounted."
	else
		echo "It's not mounted."
		mkdir -p /mnt/sdisk/usrdata/
		mkdir -p /mnt/sdisk/usrdata/update
		mkdir -p /mnt/sdisk/usrdata/user

		if [ -d "/usrdata" ];then
			rm  /usrdata -rf
		fi

		mount --bind /mnt/sdisk/usrdata/ /usrdata

	fi
}

eval partitions_check_func
eval startapp_check_func
eval usrdata_check_func

