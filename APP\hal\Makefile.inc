
#include path
INCLUDES    += -I./h
INCLUDES    += -I../fk/h
INCLUDES    += -I../resman_if/h
INCLUDES    += -I../ril_if/h
INCLUDES    += -I../dvrser_if/h

INCLUDES    += -I./source/hal/h
INCLUDES    += -I./source/adp/h
INCLUDES    += -I./source/shell
INCLUDES    += -I./source/shell/h
INCLUDES    += -I./source/plat/h

INCLUDES    += -I./source/client/dvra/h
#INCLUDES    += -I./source/client/lcda/h
INCLUDES    += -I./source/client/downloada/h

INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/qmi-framework
INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/data
INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/qmi
INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/quectel-openlinux-sdk
INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/dsutils
INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/
INCLUDES    += -I$(SDKTARGETSYSROOT)/usr/include/curl


# source files, sub directory will then add to this, all source files must use ".c" as the extension name        
SOURCES += source/hal/c/hal_uart.c \
        source/hal/c/hal_timer.c \
        source/hal/c/hal_tsk.c \
		source/hal/c/hal_socket.c \
		source/hal/c/hal_fs.c \
		source/hal/c/hal_phone.c \
		source/hal/c/hal_sm.c \
		source/hal/c/hal_gprs.c \
		source/hal/c/hal_network.c \
		source/hal/c/hal_tts.c \
		source/hal/c/hal_gpio.c \
		source/hal/c/hal_plat.c \
		source/hal/c/hal_audio.c \
		source/hal/c/hal_camera.c \
		source/hal/c/hal_video.c \
		source/hal/c/hal_ftp.c \
		source/hal/c/hal_download.c \
		source/hal/c/hal_gps.c \
		source/hal/c/hal_sensor.c \
		source/hal/c/hal_can.c \
		source/hal/c/hal_mancls.c \
		source/hal/c/hal_loader.c \
		source/hal/c/hal_app.c	\
		source/hal/c/hal_intercom.c	\
		source/hal/c/hal_url.c	\
		source/hal/c/hal_asyntask.c    
		
SOURCES += source/adp/c/fk_application.c \
        source/adp/c/adp_main.c \
		source/adp/c/adp_tools.c \
		source/adp/c/adp_misc.c \
        source/adp/c/adp_roundbuf.c \
        source/adp/c/adp_stream.c \
		source/adp/c/adp_uni2gb.c \
		source/adp/c/adp_gb2uni.c \
		source/adp/c/adp_uart.c \
		source/adp/c/adp_timer.c \
        source/adp/c/adp_tsk.c \
        source/adp/c/adp_socket.c \
        source/adp/c/adp_fs.c \
        source/adp/c/adp_phone.c \
        source/adp/c/adp_sm.c \
        source/adp/c/adp_gprs.c \
        source/adp/c/adp_network.c \
        source/adp/c/adp_tts.c \
        source/adp/c/adp_gpio.c \
        source/adp/c/adp_plat.c \
        source/adp/c/adp_audio.c \
        source/adp/c/adp_camera.c \
        source/adp/c/adp_video.c \
        source/adp/c/adp_ftp.c \
        source/adp/c/adp_download.c \
        source/adp/c/adp_gps.c \
        source/adp/c/adp_sensor.c \
        source/adp/c/adp_can.c \
        source/adp/c/adp_vm.c	\
        source/adp/c/adp_intercom.c	\
        source/adp/c/adp_url.c\
        source/adp/c/adp_asyntask.c


        
SOURCES += source/client/dvra/c/dvra_client.c \
        source/client/dvra/c/dvra_pmsg_dispatcher.c \
		source/client/dvra/c/dvra_client_drv.c \
		source/client/dvra/c/dvra_client_common.c \
		source/client/dvra/c/dvra_client_video.c \
		source/client/dvra/c/dvra_client_audio.c \
        source/client/dvra/c/dvra_client_ftp.c	\
        source/client/dvra/c/dvra_client_intercom.c

#SOURCES += source/client/lcda/c/lcda_client.c \
#		source/client/lcda/c/lcda_pmsg_dispatcher.c \
#		source/client/lcda/c/lcda_client_drv.c



SOURCES += source/client/downloada/c/dla_client.c \
		source/client/downloada/c/dla_pmsg_dispatcher.c \
		source/client/downloada/c/dla_client_drv.c \
		source/client/downloada/c/dla_client_common.c \
		source/client/downloada/c/dla_client_download.c \
		source/client/downloada/c/dla_client_log.c


		
# sub directories, sub directory will then add to this
SUBDIRS		+= ./source/hal/c
SUBDIRS		+= ./source/adp/c
SUBDIRS		+= ./source/client/dvra/c
SUBDIRS		+= ./source/client/lcda/c
SUBDIRS		+= ./source/client/downloada/c

