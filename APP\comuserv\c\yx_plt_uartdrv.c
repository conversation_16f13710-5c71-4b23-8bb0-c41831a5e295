/***************************************************************
*
*   文件名:    yx_plt_uartdrv.c
*   创建者:    RF@YAXON
*
*　 版本号:    1.0
*   文件描述:  通用输入输出接口封装模块
****************************************************************/
#define _XOPEN_SOURCE
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <errno.h>
#include <dirent.h>
#include <sys/ioctl.h>
#include <sys/types.h>
#include <sys/time.h>
#include <sys/select.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <pthread.h> 
#include <sys/termios.h>

#include "yx_plt_system.h"
#include "yx_plt_uartdrv.h"

/************************************************************************************************************************
程序调试函数及变量  ----start
************************************************************************************************************************/
#define MSG_ERR 	1
#define MSG_WARN 	2
#define MSG_INFO 	3
#define MSG_DBG 	4
#define MSG_NOR 	2

#define MSG_HEAD 	("uartdrv")

#define PRTMSG(level, fmt, args...)\
do {\
	if (level <= MSG_NOR) {\
	    if (level <= MSG_ERR) {\
		    printf("%s, %s, line %d: " fmt,__FILE__,__FUNCTION__,__LINE__, ##args);\
	    } else {\
		    printf("%s:" fmt, MSG_HEAD, ##args);\
		}\
	}\
} while(0)

/************************************************************************************************************************
程序调试函数及变量  ----end
************************************************************************************************************************/
/*******************************************************************
                    MACRO DEFINE
*******************************************************************/
#define UART_STATUS_READY           0x01
#define UART_STATUS_INIT            0x80
#define UART_STATUS_MASK            0x81

#define MIN(x, y) ((x) < (y) ? (x) : (y))

/*
********************************************************************************
*                  DEFINE CYCLEBUF STRUCT
********************************************************************************
*/
typedef struct {
    INT8U   *buf;                               // store data
    INT32U  size;                               // cycle buffer size
    INT32U  in;                                 // next write position in buffer
    INT32U  out;                                // next read position in buffer
} CYCLE_BUFFER_T;

/*
********************************************************************************
*                  DEFINE UART PORT STRUCT
********************************************************************************
*/
typedef struct {
    INT8U           status;                     // 是否已初始化的
    INT8U           *r_cyclebuf;                // 指向串口数据接收缓冲区的指针
    INT32S          com_fd;                     // 打开串口的句柄
    CYCLE_BUFFER_T  r_cycle;                    // 接收环形缓冲
    pthread_mutex_t lock;
} UART_PORT_T;

/*
********************************************************************************
*                  DEFINE UARTDRV MODULE VARIANT
********************************************************************************
*/
#ifdef UART_DEF
#undef UART_DEF
#endif
#define UART_DEF(UART_PORT_, DEVICE_TYPE, DEVICE_NAME_, CIRBUF_LEN_)\
        {DEVICE_TYPE, DEVICE_NAME_, CIRBUF_LEN_},

static const UART_REG_T s_uart_reg_tbl[] = {
        #include "yx_plt_uartmapdef.h"
        {0, NULL, 0}
};

static speed_t speed_arr[] = {B115200, B57600, B38400, B19200, B9600, B4800, B2400, B1200, B300};
static INT32U baud_arr[]   = {115200,  57600,  38400,  19200,  9600,  4800,  2400,  1200,  300};

static BOOLEAN s_uart_recv_thread_exit;

static UART_PORT_T  s_uart_port[MAX_UART];

static int pipe_fd[2];

static void yx_uart_lib_init(void) __attribute__((constructor)); 	//告诉gcc把这个函数扔到init section
static void yx_uart_lib_fini(void) __attribute__((destructor));  	//告诉gcc把这个函数扔到fini section

static void YX_InitCycleBuffer(CYCLE_BUFFER_T *cycle, INT8U *mem, INT32U memsize)
{
    INT8U *mallocptr;
    
    PLT_ASSERT(cycle != NULL);
    PLT_ASSERT(mem != NULL);
    PLT_ASSERT((memsize & (memsize -1)) == 0);          // size must be power of 2

    cycle->size = memsize;
    cycle->buf  = mem;
    cycle->in   = 0;
    cycle->out  = 0;
}

static void YX_ResetCycleBuf(CYCLE_BUFFER_T *cycle)
{
    PLT_ASSERT(cycle != NULL);
    cycle->in   = 0;
    cycle->out  = 0;
}

static INT32U YX_ReadBlockCycleBuf(CYCLE_BUFFER_T *cycle, INT8U *dstptr, INT32U dstlen)
{
    INT32U tmplen;
    
    PLT_ASSERT(cycle != NULL);
    PLT_ASSERT(dstptr != NULL);
    
    dstlen = MIN(dstlen, cycle->in - cycle->out);
    tmplen = MIN(dstlen, cycle->size - (cycle->out & (cycle->size - 1)));
    memcpy(dstptr, cycle->buf + (cycle->out & (cycle->size - 1)), tmplen);
    memcpy(dstptr + tmplen, cycle->buf, dstlen - tmplen);
    cycle->out += dstlen;
    return dstlen;
}

static BOOLEAN YX_WriteBlockCycleBuf(CYCLE_BUFFER_T *cycle, INT8U *bptr, INT32U blksize)
{
    INT32U tmplen;
    
    PLT_ASSERT(cycle != NULL);
    PLT_ASSERT(bptr != NULL);
    
    blksize = MIN(blksize, cycle->size - cycle->in + cycle->out);
    tmplen = MIN(blksize, cycle->size - (cycle->in & (cycle->size - 1)));
    memcpy(cycle->buf + (cycle->in & (cycle->size - 1)), bptr, tmplen);
    memcpy(cycle->buf, bptr + tmplen, blksize - tmplen);
    cycle->in += blksize;
    return true;
}

static BOOLEAN YX_SetUartAttr(INT8U ch, INT32U baud)
{
    INT8U i;
    INT32S tty_fd;
    struct termios options;
    
    tty_fd = s_uart_port[ch].com_fd;
    if(tcgetattr(tty_fd, &options) != 0) {
        perror("GetSerialAttr");
        return false;
    }
    
    options.c_iflag &= ~(IGNBRK|BRKINT|IGNPAR|PARMRK|INPCK|ISTRIP|INLCR|IGNCR|ICRNL|IUCLC|IXON|IXOFF|IXANY); 
    options.c_lflag &= ~(ECHO|ECHONL|ISIG|IEXTEN|ICANON);
    options.c_oflag &= ~OPOST;
    
    for (i = 0; i < sizeof(baud_arr) / sizeof(baud_arr[0]); i++) {
        if (baud == baud_arr[i]) {
            tcflush(tty_fd, TCIOFLUSH);
            cfsetispeed(&options, speed_arr[i]);
            cfsetospeed(&options, speed_arr[i]);
            if (tcsetattr(tty_fd, TCSANOW, &options) != 0) {
                PRTMSG(MSG_ERR, "Set UART COM:%s com Attr\n", s_uart_reg_tbl[ch].device_name);
                perror("tcsetattr."); 
                return false;
            }
            tcflush(tty_fd, TCIOFLUSH);
            break;
        }
    }
    if (i >= sizeof(baud_arr) / sizeof(baud_arr[0])) {
        PRTMSG(MSG_ERR, "Set UART baud error! baud is:%d\n", baud);
    }
    return true;
}

static BOOLEAN YX_OpenTTYOperation(INT8U ch, INT32U baud)
{
    INT8U i, k, mainch;
    INT32S tty_fd, filenum, len, ret;
    char usbserialpath[50];
    char *slavepath;
    struct termios options;
    struct dirent **namelist = NULL;
    
    if (s_uart_reg_tbl[ch].device_type == UART_PHY) {
        for (i = 0; i < 3; i++) {
            tty_fd = open(s_uart_reg_tbl[ch].device_name, O_RDWR | O_NOCTTY | O_NONBLOCK);
            if (-1 != tty_fd) break;
        }
        if (-1 == tty_fd) {
            PRTMSG(MSG_ERR, "Open uart COM:%s fail!\n", s_uart_reg_tbl[ch].device_name);
            perror("");
            return false;
        }
        s_uart_port[ch].com_fd = tty_fd;
    	return YX_SetUartAttr(ch, baud);
    } else if (s_uart_reg_tbl[ch].device_type == UART_USB) {
        if (access(s_uart_reg_tbl[ch].device_name, F_OK)) {
            PRTMSG(MSG_ERR,"no usb device(%s) checked!\n", s_uart_reg_tbl[ch].device_name);
            return false;
        } else {
            filenum = scandir(s_uart_reg_tbl[ch].device_name, &namelist, 0, 0);
            for (i = 0; i < filenum; i++) {
                if (NULL == strstr(namelist[i]->d_name, "ttyUSB")) {
                    continue;
                } else {
                    len = sprintf(usbserialpath, "/dev/ttyUSB", (namelist[i]->d_name + 6));
                    usbserialpath[len] = 0;
                    tty_fd = open(usbserialpath, O_RDWR | O_NOCTTY | O_NONBLOCK);
                    if (-1 == tty_fd) {
                        PRTMSG(MSG_ERR,"open usb serial device f(%s)ail!\n", usbserialpath);
                        continue;
                    }
                    break;
                }
            }
		    if (namelist) {
                for (k = 0; k < filenum; k++ ) {
				    free(namelist[k]);
			    }
			    free(namelist);
			    namelist = NULL;
		    }
            if (i >= filenum) {
                PRTMSG(MSG_ERR,"device node is not exist!\n");
                return false;
            }
        }
        PRTMSG(MSG_WARN, "Open uart COM:%s success!\n", usbserialpath);
        s_uart_port[ch].com_fd = tty_fd;
    	return YX_SetUartAttr(ch, baud);
    } else if (s_uart_reg_tbl[ch].device_type == UART_PTYMAIN) {
        tty_fd = open(s_uart_reg_tbl[ch].device_name, O_RDWR | O_NOCTTY | O_NONBLOCK);
        if (-1 == tty_fd) {
            PRTMSG(MSG_ERR, "Open uart COM:%s fail!\n", s_uart_reg_tbl[ch].device_name);
            perror("");
            return false;
        }
       grantpt(tty_fd);
	    unlockpt(tty_fd);
        s_uart_port[ch].com_fd = tty_fd;
    	return YX_SetUartAttr(ch, baud);
    } else if (s_uart_reg_tbl[ch].device_type == UART_PTYSLAVE) {
        mainch = (INT32U)(s_uart_reg_tbl[ch].device_name);
        if (s_uart_port[mainch].status & UART_STATUS_MASK) {
            slavepath = ptsname(s_uart_port[mainch].com_fd);
            tty_fd = open(slavepath, O_RDWR | O_NOCTTY | O_NONBLOCK);
            if (-1 == tty_fd) {
                PRTMSG(MSG_ERR, "Open uart COM:%s fail!\n", slavepath);
                perror("");
                return false;
            }
            PRTMSG(MSG_ERR, "Open uart COM:%s success!\n", slavepath);
            s_uart_port[ch].com_fd = tty_fd;
    	    return YX_SetUartAttr(ch, baud);
    	}
    	return false;
    } else {
        PRTMSG(MSG_ERR, "Open uart COM:%d fail! error device_type\n", s_uart_reg_tbl[ch].device_type);
        return false;
    }
}

BOOLEAN YX_PLT_InitUart(INT8U ch, INT32U baud)
{
    unsigned char sendchar;
    
    PLT_ASSERT(ch < MAX_UART);
    
    pthread_mutex_lock(&s_uart_port[ch].lock);
    if (s_uart_port[ch].status & UART_STATUS_MASK) {
        PRTMSG(MSG_ERR, "UART %d has already init!!,status=%x\n", ch, s_uart_port[ch].status);
        YX_ResetCycleBuf(&s_uart_port[ch].r_cycle);
        YX_SetUartAttr(ch, baud);
        pthread_mutex_unlock(&s_uart_port[ch].lock);
        return true;
    }
    if (!YX_OpenTTYOperation(ch, baud)) {
        goto END_INIT;
    }
    if (s_uart_port[ch].r_cyclebuf == NULL) {
        s_uart_port[ch].r_cyclebuf = (INT8U *) malloc(s_uart_reg_tbl[ch].buflen);
    }
    if (s_uart_port[ch].r_cyclebuf == NULL) {
        PRTMSG(MSG_ERR, "malloc membuf error!!\n");
        goto END_INIT;
    }
    YX_InitCycleBuffer(&s_uart_port[ch].r_cycle, s_uart_port[ch].r_cyclebuf, s_uart_reg_tbl[ch].buflen);
    s_uart_port[ch].status = (UART_STATUS_READY | UART_STATUS_INIT);
    pthread_mutex_unlock(&s_uart_port[ch].lock);
    write(pipe_fd[1], &sendchar, 1);
    return true;
END_INIT:
    if (s_uart_port[ch].r_cyclebuf) {
        free(s_uart_port[ch].r_cyclebuf);
        s_uart_port[ch].r_cyclebuf = NULL;
    }
    if (s_uart_port[ch].com_fd != -1) {
        close(s_uart_port[ch].com_fd);
        s_uart_port[ch].com_fd = -1;
    }
    s_uart_port[ch].status = 0;   
    pthread_mutex_unlock(&s_uart_port[ch].lock);
    return false;
}

static void YX_UARTRecvThreadProc(void *arg)
{
    fd_set r_fds;
    int i, maxfds, ret, readlen;
    unsigned char buf[512];
    
    sleep(1);
    while (!s_uart_recv_thread_exit) {
        FD_ZERO(&r_fds);
        //FD_ZERO(&err_fds);
        FD_SET(pipe_fd[0], &r_fds);
        maxfds = pipe_fd[0];
        for (i = 0; i < sizeof(s_uart_port) / sizeof(s_uart_port[0]); i++) {
            pthread_mutex_lock(&s_uart_port[i].lock);
            if ((s_uart_port[i].status & (UART_STATUS_INIT))) {
                FD_SET(s_uart_port[i].com_fd, &r_fds);
                //FD_SET(s_uart_port[i].com_fd, &err_fds);
                if (s_uart_port[i].com_fd > maxfds) {
                    maxfds = s_uart_port[i].com_fd;
                }
            }
            pthread_mutex_unlock(&s_uart_port[i].lock);
        }
        ret = select(maxfds + 1, &r_fds, NULL, NULL, NULL);
        if (ret < 0) {
            PRTMSG(MSG_ERR, "select error\n");
            perror("select error\n");
            continue;
        } else {
            if (FD_ISSET(pipe_fd[0], &r_fds)) {
                read(pipe_fd[0], buf, sizeof(buf));
                usleep(100 * 1000);
PRTMSG(MSG_DBG, "YX_UARTRecvThreadProc_2\n");
            } else {
                for (i = 0; i < sizeof(s_uart_port) / sizeof(s_uart_port[0]); i++) {
                    pthread_mutex_lock(&s_uart_port[i].lock);
                    if ((s_uart_port[i].status & (UART_STATUS_INIT))) {
                        if (FD_ISSET(s_uart_port[i].com_fd, &r_fds)) {
                            readlen = read(s_uart_port[i].com_fd, buf, sizeof(buf));
                            if (readlen > 0) {
                                YX_WriteBlockCycleBuf(&s_uart_port[i].r_cycle, buf, readlen);
                            } else {
                                if (s_uart_reg_tbl[i].device_type != UART_PHY) {
                                    PRTMSG(MSG_WARN, "err recv! now close uart %d\n", i);
                                    s_uart_port[i].status = 0;
                                    close(s_uart_port[i].com_fd);
                                    s_uart_port[i].com_fd = -1;
                                    YX_ResetCycleBuf(&s_uart_port[i].r_cycle);
                                } else {
                                    PRTMSG(MSG_ERR, "select error:port=%d\n", i);
                                }
                            }
PRTMSG(MSG_DBG, "YX_UARTRecvThreadProc_3:readlen=%d\n", readlen);
                        } 
                        /*
                        if (FD_ISSET(s_uart_port[i].com_fd, &err_fds)) {
PRTMSG(MSG_DBG, "YX_UARTRecvThreadProc_4\n");
                            if (s_uart_reg_tbl[i].device_type == UART_USB) {
                                s_uart_port[i].status &= (~UART_STATUS_INIT);
                                close(s_uart_port[i].com_fd);
                                s_uart_port[i].com_fd = -1;
                                YX_ResetCycleBuf(&s_uart_port[i].r_cycle);
                            } else {
                                PRTMSG(MSG_ERR, "select error:port=%d\n", i);
                            }
                        }
                        */
                    }
                    pthread_mutex_unlock(&s_uart_port[i].lock);
                }
            }
        }
    }
    pthread_exit(NULL);
}

void YX_PLT_ReInitUart(INT8U ch, INT32U baud)
{
    unsigned char sendchar;
    
    PLT_ASSERT(ch < MAX_UART);
    
    pthread_mutex_lock(&s_uart_port[ch].lock);
    if (s_uart_reg_tbl[ch].device_type == UART_USB) {
        if (s_uart_port[ch].status & (UART_STATUS_INIT)) {
            s_uart_port[ch].status = 0;
            close(s_uart_port[ch].com_fd);
            s_uart_port[ch].com_fd = -1;
            YX_ResetCycleBuf(&s_uart_port[ch].r_cycle);
        }
        if (YX_OpenTTYOperation(ch, baud)) {
            s_uart_port[ch].status = (UART_STATUS_READY | UART_STATUS_INIT);
            pthread_mutex_unlock(&s_uart_port[ch].lock);
            write(pipe_fd[1], &sendchar, 1);
        } else {
            pthread_mutex_unlock(&s_uart_port[ch].lock);
        }  
    } else {
        YX_ResetCycleBuf(&s_uart_port[ch].r_cycle);
        YX_SetUartAttr(ch, baud);
        pthread_mutex_unlock(&s_uart_port[ch].lock);
    }
}

BOOLEAN YX_PLT_UartWrite(INT8U ch, INT8U data)
{
    INT8U tmpdata;
    
    PLT_ASSERT(ch < MAX_UART);
    if ((s_uart_port[ch].status & UART_STATUS_INIT) != UART_STATUS_INIT) {
        return FALSE;
    }
    tmpdata = data;
    if (write(s_uart_port[ch].com_fd, &tmpdata, 1) == 1) {
        return true;
    } else {

        return false;
    }
}

BOOLEAN YX_PLT_UartWriteBlock(INT8U ch, INT8U* sdata, INT16U slen)
{
    PLT_ASSERT(ch < MAX_UART);
    if ((s_uart_port[ch].status & UART_STATUS_INIT) != UART_STATUS_INIT) {
        return FALSE;
    }
    if (write(s_uart_port[ch].com_fd, sdata, slen) == slen) {
        return true;
    } else {
        return false;
    }
}

INT16S YX_PLT_UartRead(INT8U ch)
{
    INT8U tmpdata;
        
    PLT_ASSERT(ch < MAX_UART);
    if ((s_uart_port[ch].status & UART_STATUS_INIT) != UART_STATUS_INIT) {
        return -2;
    }
    if (1 == YX_ReadBlockCycleBuf(&s_uart_port[ch].r_cycle, &tmpdata, 1)) {
        return tmpdata;
    } else {
        return -1;
    }
}

INT16U YX_PLT_UartReadBlock(INT8U ch, INT8U *dptr, INT16U wishlen)
{
    INT32U len;
    
    PLT_ASSERT(ch < MAX_UART);
    PLT_ASSERT(dptr != NULL);
    
    if ((s_uart_port[ch].status & UART_STATUS_INIT) != UART_STATUS_INIT) {
        return FALSE;
    }
    return YX_ReadBlockCycleBuf(&s_uart_port[ch].r_cycle, dptr, wishlen);
}

BOOLEAN YX_UartGetAttrib(INT8U ch, UART_REG_T *regattr)
{
    PLT_ASSERT(ch < MAX_UART);
    PLT_ASSERT(regattr != NULL);
    
    memcpy((INT8U *)regattr, (INT8U *)&s_uart_reg_tbl[ch], sizeof(UART_REG_T));
    return TRUE;
}

INT32S YX_UartGetPortFilefd(INT8U ch)
{
    PLT_ASSERT(ch < MAX_UART);
    if ((s_uart_port[ch].status & UART_STATUS_INIT) != UART_STATUS_INIT) {  
        return -1;
    }
    return s_uart_port[ch].com_fd;
}

//动态库入口函数
void yx_uart_lib_init(void)
{
    INT8U i;
    pthread_t uart_recv_pthread;
    
    printf("\n+ uartlib.so\n");
    for (i = 0; i < sizeof(s_uart_port) / sizeof(s_uart_port[0]); i++) {
        pthread_mutex_init(&s_uart_port[i].lock, NULL);
        memset(&s_uart_port[i].r_cycle, 0, sizeof(CYCLE_BUFFER_T));
        s_uart_port[i].status = 0;
        s_uart_port[i].r_cyclebuf = NULL;
        s_uart_port[i].com_fd = -1;
    }
    if (pipe(pipe_fd) < 0) {        // 创建一个管道pipe_fd[0]是读端，pipe_fd[1]是写端
        PRTMSG(MSG_ERR, "create pipe failed\n");
        return;
    }
    // 创建数据接收处理线程
    s_uart_recv_thread_exit = FALSE;
    if (pthread_create(&uart_recv_pthread, NULL, (void *)YX_UARTRecvThreadProc, NULL)) {
        PRTMSG(MSG_ERR, "create uart recv thread failed\n");
    }
}

//动态库退出口函数
void yx_uart_lib_fini(void)
{
    INT8U i;
    INT8U tmpdata;
	
    printf("\n- uartlib.so\n");
    s_uart_recv_thread_exit = TRUE;
    
    write(pipe_fd[1], &tmpdata, 1);
    usleep(1000);
    for (i = 0; i < sizeof(s_uart_port) / sizeof(s_uart_port[0]); i++) {
        s_uart_port[i].status = 0;
        if (s_uart_port[i].com_fd != -1) {
            close(s_uart_port[i].com_fd);
            s_uart_port[i].com_fd = -1;
        }
        if (s_uart_port[i].r_cyclebuf) {
            free(s_uart_port[i].r_cyclebuf);
            s_uart_port[i].r_cyclebuf = NULL;
        }
    }
    close(pipe_fd[0]);
    close(pipe_fd[1]);
}

