# 嵌入式 Linux 测试用例模板

| 字段         | 内容说明 |
|--------------|----------|
| **用例编号** | TC-XXX-YYY |
| **用例名称** | 简要描述测试目标 |
| **所属模块** | 驱动 / 内核 / 应用 / 网络 / 存储 / 其他 |
| **测试类型** | 功能 / 性能 / 稳定性 / 兼容性 / 安全性 |
| **优先级**   | 高 / 中 / 低 |
| **前置条件** | - 系统启动正常<br>- 测试设备已连接 (UART / I2C / SPI / 网络)<br>- 驱动或模块已加载 |

---

| **测试步骤** | **说明/命令** |
|--------------|---------------|
| Step 1       | 确认测试环境（内核版本、根文件系统版本、交叉编译器版本） |
| Step 2       | 确认依赖的模块或库已存在 |
| Step 3       | 执行命令：`command` 或 `./test_app arg1 arg2` |
| Step 4       | 检查内核日志：`dmesg | tail -n 50` |
| Step 5       | 验证设备节点：`ls /dev/...` 或 `cat /proc/...` |

---

| **预期结果** | **说明** |
|--------------|----------|
| 驱动加载正常 | 无错误日志 |
| 输出正确     | 符合协议或数据格式 |
| 性能达标     | 延迟 < 10ms，吞吐量 > 100Mbps |
| 系统稳定     | 无崩溃、死机、异常重启 |

---

| **实际结果** | **结论** |
|--------------|----------|
| 测试现象记录 | [ ] 通过 / [ ] 失败（描述失败现象） |

---

| **附加信息** | **说明** |
|--------------|----------|
| 测试日志     | `/var/log/test_xxx.log` |
| 内核日志     | `dmesg | tail -n 50` |
| 其他资料     | 截图 / 波形图 / 抓包文件 |
