# Makefile.param 
# Only global variable should be defined here.
# All the variables must be used as "export" and "?=".
# Otherwise, there will be some errors, when Makefile.param is nested.

# Use this file as the following sample
# ifeq ($(PARAM_FILE), )
#     PARAM_FILE:=../Makefile.param
#     include $(PARAM_FILE)
# endif

# Define the default OS link directory.

ifeq ($(SDKTARGETSYSROOT), )
    SDKTARGETSYSROOT=$(subst /x86_64-oesdk-linux/usr/bin/arm-oe-linux-gnueabi/arm-oe-linux-gnueabi-gcc,,$(shell which arm-oe-linux-gnueabi-gcc))/armv7a-vfp-neon-oe-linux-gnueabi
endif
    QL_TOOLS_PATH   ?=$(SDKTARGETSYSROOT)/../x86_64-oesdk-linux/usr/bin/tools
