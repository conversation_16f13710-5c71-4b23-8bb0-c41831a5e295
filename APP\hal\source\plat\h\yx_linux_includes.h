﻿/********************************************************************************
**
** 文件名:     yx_linux_includes.h
** 版权所有:   (c) 2007-2008 厦门雅迅网络股份有限公司
** 文件描述:   linux系统常用头文件
**
*********************************************************************************
**             修改历史记录
**===============================================================================
**| 日期       | 作者   |  修改记录
**===============================================================================
**| 2011/01/15 | 叶德焰 |  创建第一版本
*********************************************************************************/
#ifndef _H_YX_LINUX_INCLUDES_
#define _H_YX_LINUX_INCLUDES_

#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdarg.h>
#include <dirent.h>
#include <termios.h>
#include <fcntl.h>
#include <stddef.h>
#include <time.h>
#include <errno.h>
#include <pthread.h>
#include <sys/vfs.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <sys/ioctl.h>
#include <sys/ipc.h>
#include <sys/sem.h>
#include <sys/shm.h>
#include <sys/msg.h>
#include <sys/mman.h>
#include <sys/time.h>
#include <sys/socket.h>
#include <linux/fb.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <netdb.h>

#include <arpa/inet.h>
#include <netinet/in.h>
#include <netinet/tcp.h>
#include <sys/time.h>
#include <sys/socket.h>
#include <sys/wait.h>
#include <sys/types.h>
//#include <sys/ipc.h>
//#include <sys/sem.h>
#include <sys/stat.h>
#include <net/if.h>


//fs
#include <errno.h>
#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <dirent.h>

//socket
#include <sys/select.h>
#include <sys/socket.h>
#include <asm-generic/errno.h>
#include <errno.h>
#include <fcntl.h>
#include <arpa/inet.h>
#include <string.h>

#endif
